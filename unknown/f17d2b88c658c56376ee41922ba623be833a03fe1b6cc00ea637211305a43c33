<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $data['title'] ?? 'لوحة تحكم الأدمن - نظام حكيم' ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><circle cx='50' cy='50' r='45' fill='%23007BFF'/><path d='M30 25c-8 0-15 7-15 15v10c0 3 2 5 5 5s5-2 5-5v-10c0-3 2-5 5-5s5 2 5 5v25c0 8 7 15 15 15s15-7 15-15v-25c0-3 2-5 5-5s5 2 5 5v10c0 3 2 5 5 5s5-2 5-5v-10c0-8-7-15-15-15-3 0-6 1-8 3-2-2-5-3-8-3z' fill='white'/><circle cx='50' cy='75' r='8' fill='white'/></svg>">
    <link rel="apple-touch-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><circle cx='50' cy='50' r='45' fill='%23007BFF'/><path d='M30 25c-8 0-15 7-15 15v10c0 3 2 5 5 5s5-2 5-5v-10c0-3 2-5 5-5s5 2 5 5v25c0 8 7 15 15 15s15-7 15-15v-25c0-3 2-5 5-5s5 2 5 5v10c0 3 2 5 5 5s5-2 5-5v-10c0-8-7-15-15-15-3 0-6 1-8 3-2-2-5-3-8-3z' fill='white'/><circle cx='50' cy='75' r='8' fill='white'/></svg>">
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-blue: #007BFF;
            --primary-sky-blue: #87CEEB;
            --primary-white: #FFFFFF;
            --primary-black: #333333;
            --light-gray: #F8F9FA;
            --border-color: #E0E0E0;
            --shadow: 0 2px 10px rgba(0,0,0,0.1);
            --shadow-hover: 0 4px 20px rgba(0,0,0,0.15);
            --admin-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: var(--light-gray);
            color: var(--primary-black);
        }

        .sidebar {
            min-height: 100vh;
            background: var(--admin-gradient);
            color: var(--primary-white);
            position: fixed;
            top: 0;
            right: 0;
            width: 250px;
            z-index: 1000;
            transition: all 0.3s ease;
            box-shadow: var(--shadow);
            overflow-y: auto;
            max-height: 100vh;
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.9);
            padding: 15px 20px;
            border-radius: 10px;
            margin: 5px 15px;
            transition: all 0.3s ease;
            font-weight: 500;
            border: 1px solid transparent;
            text-decoration: none;
        }

        .sidebar .nav-link:hover {
            background-color: rgba(255,255,255,0.15);
            color: var(--primary-white);
            transform: translateX(-3px);
            border-color: rgba(255,255,255,0.2);
        }

        .sidebar .nav-link.active {
            background-color: var(--primary-white);
            color: var(--primary-blue);
            font-weight: 600;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
        }

        .main-content {
            margin-right: 250px;
            padding: 25px;
            transition: all 0.3s ease;
            min-height: 100vh;
        }

        .navbar {
            background: var(--primary-white);
            box-shadow: var(--shadow);
            margin-bottom: 25px;
            border-radius: 10px;
            padding: 15px 20px;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: var(--shadow);
            margin-bottom: 25px;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .card:hover {
            box-shadow: var(--shadow-hover);
            transform: translateY(-2px);
        }

        .card-header {
            background: var(--admin-gradient);
            color: var(--primary-white);
            border-radius: 0 !important;
            border: none;
            padding: 20px;
            font-weight: 600;
        }

        .stats-card {
            background: var(--admin-gradient);
            color: var(--primary-white);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            transition: all 0.3s ease;
            box-shadow: var(--shadow);
        }

        .stats-card:hover {
            box-shadow: var(--shadow-hover);
            transform: translateY(-3px);
        }

        .stats-card .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stats-card .stats-icon {
            opacity: 0.8;
            font-size: 3rem;
            margin-bottom: 15px;
        }

        .btn-primary {
            background: var(--admin-gradient);
            border: none;
            border-radius: 10px;
            padding: 12px 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-hover);
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .table th {
            background: var(--admin-gradient);
            color: var(--primary-white);
            border: none;
            font-weight: 600;
            padding: 15px;
        }

        .table td {
            padding: 15px;
            border-color: var(--border-color);
            vertical-align: middle;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(102, 126, 234, 0.05);
        }

        .badge {
            padding: 8px 12px;
            border-radius: 20px;
            font-weight: 500;
            font-size: 0.8rem;
        }

        .admin-header {
            background: var(--admin-gradient);
            color: var(--primary-white);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 25px;
            text-align: center;
        }

        .quick-actions {
            background: var(--primary-white);
            border-radius: 15px;
            padding: 20px;
            box-shadow: var(--shadow);
            margin-bottom: 25px;
        }

        .quick-actions .btn {
            margin: 5px;
            border-radius: 10px;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-right: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="p-3">
            <div class="text-center mb-4">
                <h4 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    لوحة تحكم الأدمن
                </h4>
                <small class="text-white-50">نظام إدارة العيادات</small>
            </div>
            
            <nav class="nav flex-column">
                <a class="nav-link" href="dashboard.php" data-page="dashboard">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    الرئيسية
                </a>
                <a class="nav-link" href="clinics.php" data-page="clinics">
                    <i class="fas fa-hospital me-2"></i>
                    العيادات
                </a>
                <a class="nav-link" href="subscriptions.php" data-page="subscriptions">
                    <i class="fas fa-credit-card me-2"></i>
                    الاشتراكات
                </a>
                <a class="nav-link" href="users.php" data-page="users">
                    <i class="fas fa-users me-2"></i>
                    المستخدمين
                </a>
                <a class="nav-link" href="revenue.php" data-page="revenue">
                    <i class="fas fa-chart-line me-2"></i>
                    الإيرادات
                </a>
                <a class="nav-link" href="messages.php" data-page="messages">
                    <i class="fas fa-envelope me-2"></i>
                    الرسائل
                </a>
                <a class="nav-link" href="settings.php" data-page="settings">
                    <i class="fas fa-cog me-2"></i>
                    الإعدادات
                </a>
                <hr class="text-white-50">
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt me-2"></i>
                    تسجيل الخروج
                </a>
            </nav>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Header -->
        <div class="admin-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-0">
                        <i class="fas fa-shield-alt me-3"></i>
                        لوحة تحكم الأدمن
                    </h1>
                    <p class="mb-0 mt-2">مرحباً بك في نظام إدارة عيادات حكيم</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-outline-light" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row">
            <div class="col-md-6 col-lg-3">
                <div class="stats-card text-center">
                    <div class="stats-icon">
                        <i class="fas fa-hospital"></i>
                    </div>
                    <div class="stats-number"><?= $data['stats']['total_clinics'] ?? 0 ?></div>
                    <div class="stats-label">إجمالي العيادات</div>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-3">
                <div class="stats-card text-center">
                    <div class="stats-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stats-number"><?= $data['stats']['total_users'] ?? 0 ?></div>
                    <div class="stats-label">إجمالي المستخدمين</div>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-3">
                <div class="stats-card text-center">
                    <div class="stats-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stats-number"><?= number_format($data['stats']['total_revenue'] ?? 0) ?> ₪</div>
                    <div class="stats-label">إجمالي الإيرادات</div>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-3">
                <div class="stats-card text-center">
                    <div class="stats-icon">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <div class="stats-number"><?= $data['stats']['active_subscriptions'] ?? 0 ?></div>
                    <div class="stats-label">الاشتراكات النشطة</div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <h5 class="mb-3">
                <i class="fas fa-bolt me-2 text-warning"></i>
                إجراءات سريعة
            </h5>
            <div class="row">
                <div class="col-md-3 mb-2">
                    <button class="btn btn-success w-100" onclick="addClinic()">
                        <i class="fas fa-plus me-2"></i>
                        إضافة عيادة
                    </button>
                </div>
                <div class="col-md-3 mb-2">
                    <button class="btn btn-primary w-100" onclick="renewSubscription()">
                        <i class="fas fa-credit-card me-2"></i>
                        تجديد اشتراك
                    </button>
                </div>
                <div class="col-md-3 mb-2">
                    <button class="btn btn-info w-100" onclick="generateReport()">
                        <i class="fas fa-download me-2"></i>
                        تقرير مالي
                    </button>
                </div>
                <div class="col-md-3 mb-2">
                    <button class="btn btn-warning w-100" onclick="systemSettings()">
                        <i class="fas fa-cog me-2"></i>
                        إعدادات النظام
                    </button>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            الاشتراكات المنتهية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <strong><?= $data['stats']['expired_subscriptions'] ?? 0 ?></strong> اشتراك منتهي الصلاحية
                        </div>
                        <button class="btn btn-primary btn-sm" onclick="viewExpiredSubscriptions()">
                            <i class="fas fa-eye me-2"></i>
                            عرض التفاصيل
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-envelope me-2"></i>
                            آخر الرسائل
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($data['stats']['recent_contacts'])): ?>
                            <div class="list-group list-group-flush">
                                <?php foreach (array_slice($data['stats']['recent_contacts'], 0, 3) as $message): ?>
                                    <div class="list-group-item d-flex justify-content-between align-items-start">
                                        <div class="ms-2 me-auto">
                                            <div class="fw-bold"><?= htmlspecialchars($message['name']) ?></div>
                                            <small class="text-muted"><?= htmlspecialchars($message['subject']) ?></small>
                                        </div>
                                        <span class="badge bg-primary rounded-pill">
                                            <?= date('d/m', strtotime($message['created_at'])) ?>
                                        </span>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <strong>0</strong> رسالة جديدة
                            </div>
                        <?php endif; ?>
                        <button class="btn btn-info btn-sm mt-2" onclick="viewAllMessages()">
                            <i class="fas fa-eye me-2"></i>
                            عرض جميع الرسائل
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Clinic Modal -->
    <div class="modal fade" id="addClinicModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus me-2"></i>
                        إضافة عيادة جديدة
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addClinicForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم العيادة</label>
                                    <input type="text" class="form-control" id="clinicName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">نوع العيادة</label>
                                    <select class="form-select" id="clinicType" required>
                                        <option value="">اختر نوع العيادة</option>
                                        <option value="general">عيادة عامة</option>
                                        <option value="specialized">عيادة متخصصة</option>
                                        <option value="dental">عيادة أسنان</option>
                                        <option value="ophthalmology">عيادة عيون</option>
                                        <option value="cardiology">عيادة قلب</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم المالك</label>
                                    <input type="text" class="form-control" id="ownerName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="ownerEmail" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">رقم الهاتف</label>
                                    <input type="tel" class="form-control" id="phoneNumber" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">نوع الاشتراك</label>
                                    <select class="form-select" id="subscriptionType" required>
                                        <option value="">اختر نوع الاشتراك</option>
                                        <option value="basic">الخطة الأساسية - $29</option>
                                        <option value="advanced">الخطة المتقدمة - $59</option>
                                        <option value="professional">الخطة الاحترافية - $99</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">العنوان</label>
                            <textarea class="form-control" id="address" rows="3" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="notes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-success" onclick="saveClinic()">
                        <i class="fas fa-save me-2"></i>
                        حفظ العيادة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تفعيل القائمة الجانبية
        function activateNavLink() {
            const currentPage = window.location.pathname;
            const navLinks = document.querySelectorAll('.sidebar .nav-link');
            
            navLinks.forEach(link => {
                link.classList.remove('active');
                const href = link.getAttribute('href');
                
                // استخراج اسم الملف من الرابط
                const fileName = href.split('/').pop();
                const currentFileName = currentPage.split('/').pop();
                
                // التحقق من تطابق اسم الملف
                if (fileName && fileName === currentFileName) {
                    link.classList.add('active');
                }
            });
        }

        // تبديل القائمة الجانبية (للموبايل)
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('show');
        }

        // دوال الإجراءات السريعة
        function addClinic() {
            // فتح نافذة منبثقة لإضافة عيادة جديدة
            const modal = new bootstrap.Modal(document.getElementById('addClinicModal'));
            modal.show();
        }

        function saveClinic() {
            // التحقق من صحة البيانات
            const clinicName = document.getElementById('clinicName').value;
            const clinicType = document.getElementById('clinicType').value;
            const ownerName = document.getElementById('ownerName').value;
            const ownerEmail = document.getElementById('ownerEmail').value;
            const phoneNumber = document.getElementById('phoneNumber').value;
            const subscriptionType = document.getElementById('subscriptionType').value;
            const address = document.getElementById('address').value;

            if (!clinicName || !clinicType || !ownerName || !ownerEmail || !phoneNumber || !subscriptionType || !address) {
                alert('يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            // إرسال البيانات (يمكن إضافة كود AJAX هنا)
            console.log('حفظ بيانات العيادة:', {
                clinicName,
                clinicType,
                ownerName,
                ownerEmail,
                phoneNumber,
                subscriptionType,
                address
            });

            alert('تم حفظ العيادة بنجاح');
            
            // إغلاق النافذة المنبثقة
            const modal = bootstrap.Modal.getInstance(document.getElementById('addClinicModal'));
            modal.hide();
            
            // إعادة تحميل الصفحة لتحديث الإحصائيات
            setTimeout(() => {
                location.reload();
            }, 1000);
        }

        function renewSubscription() {
            // الانتقال إلى صفحة الاشتراكات
            window.location.href = 'subscriptions.php';
        }

        function generateReport() {
            // فتح نافذة منبثقة لتحميل التقرير
            if (confirm('هل تريد تحميل التقرير المالي؟')) {
                alert('سيتم تحميل التقرير المالي قريباً');
                // يمكن إضافة كود لتحميل التقرير
            }
        }

        function systemSettings() {
            // الانتقال إلى صفحة الإعدادات
            window.location.href = 'settings.php';
        }

        function viewExpiredSubscriptions() {
            // الانتقال إلى صفحة الاشتراكات مع فلتر الاشتراكات المنتهية
            window.location.href = 'subscriptions.php';
        }

        function viewAllMessages() {
            // الانتقال إلى صفحة الرسائل
            window.location.href = 'messages.php';
        }

        // تفعيل القائمة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            activateNavLink();
        });
    </script>
</body>
</html> 